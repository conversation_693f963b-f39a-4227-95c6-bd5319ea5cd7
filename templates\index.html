<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ShopSocial - <PERSON><PERSON> & <PERSON><PERSON><PERSON> n<PERSON>i</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script src="/static/js/tailwind.config.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <link rel="stylesheet" href="/static/css/style.css" />
  </head>
  <body class="min-h-screen">
    <!-- Header -->
    <header class="sticky top-0 z-50 bg-white border-b border-gray-200">
      <div class="container mx-auto px-4 py-3">
        <div class="flex items-center justify-between">
          <!-- Logo -->
          <div class="flex items-center">
            <a href="#" class="text-2xl font-['Pacifico'] text-primary">logo</a>
          </div>

          <!-- Search Bar -->
          <div class="hidden md:flex items-center flex-1 max-w-xl mx-6">
            <div class="relative w-full">
              <input
                type="text"
                class="search-input w-full py-2 pl-10 pr-4 bg-gray-100 rounded-full text-sm"
                placeholder="Tìm kiếm sản phẩm, thương hiệu, cửa hàng..."
              />
              <div
                class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 flex items-center justify-center text-gray-400"
              >
                <i class="ri-search-line"></i>
              </div>
            </div>
          </div>

          <!-- Navigation Icons -->
          <div class="flex items-center space-x-5">
            <div class="md:hidden w-8 h-8 flex items-center justify-center">
              <i class="ri-search-line text-lg"></i>
            </div>

            <div class="relative w-8 h-8 flex items-center justify-center">
              <i class="ri-shopping-cart-2-line text-lg"></i>
              <span
                class="cart-count absolute -top-1 -right-1 bg-primary text-white text-xs w-5 h-5 flex items-center justify-center rounded-full"
                >3</span
              >
            </div>

            <div class="relative w-8 h-8 flex items-center justify-center">
              <i class="ri-notification-3-line text-lg"></i>
              <span class="notification-dot"></span>
            </div>

            <div class="relative w-8 h-8 flex items-center justify-center">
              <i class="ri-message-3-line text-lg"></i>
              <span class="notification-dot"></span>
            </div>

            <div class="relative">
              <img
                src="https://readdy.ai/api/search-image?query=young%20asian%20vietnamese%20woman%20professional%20headshot%20portrait%2C%20minimalist%20background%2C%20professional%20looking&width=100&height=100&seq=avatar1&orientation=squarish"
                alt="Avatar"
                class="w-8 h-8 rounded-full object-cover"
              />
            </div>
          </div>
        </div>

        <!-- Categories Navigation -->
        <nav
          class="hidden md:flex items-center space-x-8 mt-3 text-sm font-medium"
        >
          <a href="#" class="text-primary border-b-2 border-primary pb-2"
            >Trang chủ</a
          >
          <a href="#" class="text-gray-600 hover:text-primary">Thời trang</a>
          <a href="#" class="text-gray-600 hover:text-primary">Điện tử</a>
          <a href="#" class="text-gray-600 hover:text-primary">Mỹ phẩm</a>
          <a href="#" class="text-gray-600 hover:text-primary">Nhà cửa</a>
          <a href="#" class="text-gray-600 hover:text-primary">Sức khỏe</a>
          <a href="#" class="text-gray-600 hover:text-primary">Thể thao</a>
          <a href="#" class="text-gray-600 hover:text-primary">Sách</a>
          <a href="#" class="text-gray-600 hover:text-primary">Khuyến mãi</a>
        </nav>
      </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-6">
      <!-- Stories/Highlights -->
      <div class="mb-8">
        <div class="flex space-x-4 overflow-x-auto pb-2 scrollbar-hide">
          <div class="flex flex-col items-center space-y-1 flex-shrink-0">
            <div
              class="w-16 h-16 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 p-0.5"
            >
              <div
                class="w-full h-full rounded-full border-2 border-white flex items-center justify-center bg-white"
              >
                <i class="ri-add-line text-xl text-primary"></i>
              </div>
            </div>
            <span class="text-xs">Thêm mới</span>
          </div>

          <div class="flex flex-col items-center space-y-1 flex-shrink-0">
            <div
              class="w-16 h-16 rounded-full bg-gradient-to-r from-yellow-500 to-red-500 p-0.5"
            >
              <img
                src="https://readdy.ai/api/search-image?query=fashion%20clothing%20items%2C%20minimalist%20display%2C%20clean%20background&width=100&height=100&seq=story1&orientation=squarish"
                alt="Story"
                class="w-full h-full rounded-full border-2 border-white object-cover"
              />
            </div>
            <span class="text-xs">Thời trang</span>
          </div>

          <div class="flex flex-col items-center space-y-1 flex-shrink-0">
            <div
              class="w-16 h-16 rounded-full bg-gradient-to-r from-blue-500 to-teal-500 p-0.5"
            >
              <img
                src="https://readdy.ai/api/search-image?query=electronics%20gadgets%2C%20minimalist%20display%2C%20clean%20background&width=100&height=100&seq=story2&orientation=squarish"
                alt="Story"
                class="w-full h-full rounded-full border-2 border-white object-cover"
              />
            </div>
            <span class="text-xs">Điện tử</span>
          </div>

          <div class="flex flex-col items-center space-y-1 flex-shrink-0">
            <div
              class="w-16 h-16 rounded-full bg-gradient-to-r from-pink-500 to-purple-500 p-0.5"
            >
              <img
                src="https://readdy.ai/api/search-image?query=beauty%20cosmetics%20products%2C%20minimalist%20display%2C%20clean%20background&width=100&height=100&seq=story3&orientation=squarish"
                alt="Story"
                class="w-full h-full rounded-full border-2 border-white object-cover"
              />
            </div>
            <span class="text-xs">Mỹ phẩm</span>
          </div>

          <div class="flex flex-col items-center space-y-1 flex-shrink-0">
            <div
              class="w-16 h-16 rounded-full bg-gradient-to-r from-green-500 to-teal-500 p-0.5"
            >
              <img
                src="https://readdy.ai/api/search-image?query=home%20decor%20items%2C%20minimalist%20display%2C%20clean%20background&width=100&height=100&seq=story4&orientation=squarish"
                alt="Story"
                class="w-full h-full rounded-full border-2 border-white object-cover"
              />
            </div>
            <span class="text-xs">Nhà cửa</span>
          </div>

          <div class="flex flex-col items-center space-y-1 flex-shrink-0">
            <div
              class="w-16 h-16 rounded-full bg-gradient-to-r from-red-500 to-orange-500 p-0.5"
            >
              <img
                src="https://readdy.ai/api/search-image?query=sports%20equipment%2C%20minimalist%20display%2C%20clean%20background&width=100&height=100&seq=story5&orientation=squarish"
                alt="Story"
                class="w-full h-full rounded-full border-2 border-white object-cover"
              />
            </div>
            <span class="text-xs">Thể thao</span>
          </div>

          <div class="flex flex-col items-center space-y-1 flex-shrink-0">
            <div
              class="w-16 h-16 rounded-full bg-gradient-to-r from-indigo-500 to-blue-500 p-0.5"
            >
              <img
                src="https://readdy.ai/api/search-image?query=books%20and%20stationery%2C%20minimalist%20display%2C%20clean%20background&width=100&height=100&seq=story6&orientation=squarish"
                alt="Story"
                class="w-full h-full rounded-full border-2 border-white object-cover"
              />
            </div>
            <span class="text-xs">Sách</span>
          </div>
        </div>
      </div>

      <!-- Flash Sale Banner -->
      <div
        class="mb-8 bg-gradient-to-r from-red-500 to-orange-500 rounded-lg overflow-hidden"
      >
        <div class="flex flex-col md:flex-row">
          <div class="p-6 md:p-8 flex-1 text-white">
            <h2 class="text-2xl md:text-3xl font-bold mb-2">
              Flash Sale Cuối Tuần
            </h2>
            <p class="mb-4 text-white/80">
              Giảm đến 70% cho hàng ngàn sản phẩm
            </p>
            <div class="flex space-x-4 mb-6">
              <div class="bg-white/20 rounded p-2 text-center">
                <div class="text-xl font-bold">12</div>
                <div class="text-xs">Giờ</div>
              </div>
              <div class="bg-white/20 rounded p-2 text-center">
                <div class="text-xl font-bold">45</div>
                <div class="text-xs">Phút</div>
              </div>
              <div class="bg-white/20 rounded p-2 text-center">
                <div class="text-xl font-bold">30</div>
                <div class="text-xs">Giây</div>
              </div>
            </div>
            <button
              class="bg-white text-red-500 px-6 py-2 rounded-button font-medium hover:bg-white/90 whitespace-nowrap"
            >
              Mua ngay
            </button>
          </div>
          <div class="md:w-2/5">
            <img
              src="https://readdy.ai/api/search-image?query=shopping%20sale%20promotion%20with%20various%20products%20on%20display%2C%20vibrant%20colorful%20background%2C%20professional%20product%20photography&width=600&height=400&seq=flashsale&orientation=landscape"
              alt="Flash Sale"
              class="w-full h-full object-cover object-top"
            />
          </div>
        </div>
      </div>

      <!-- Newsfeed -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Products will be dynamically loaded here -->
      </div>

      <!-- Load More Button -->
      <div class="mt-8 text-center">
        <button
          class="bg-white border border-gray-300 text-gray-700 px-6 py-3 rounded-button font-medium hover:bg-gray-50 whitespace-nowrap"
        >
          <i class="ri-refresh-line mr-2"></i> Xem thêm
        </button>
      </div>
    </main>

    <!-- Chat Floating Button -->
    <div class="fixed bottom-20 right-6 z-40 md:bottom-6">
      <button
        class="w-12 h-12 bg-primary text-white rounded-full shadow-lg flex items-center justify-center"
      >
        <i class="ri-message-3-line text-xl"></i>
      </button>
    </div>

    <!-- Mobile Navigation -->
    <nav
      class="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-40"
    >
      <div class="flex justify-around">
        <a href="#" class="flex flex-col items-center py-2 text-primary">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-home-5-fill text-lg"></i>
          </div>
          <span class="text-xs mt-1">Trang chủ</span>
        </a>
        <a href="#" class="flex flex-col items-center py-2 text-gray-500">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-search-line text-lg"></i>
          </div>
          <span class="text-xs mt-1">Tìm kiếm</span>
        </a>
        <a href="#" class="flex flex-col items-center py-2 text-gray-500">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-shopping-bag-line text-lg"></i>
          </div>
          <span class="text-xs mt-1">Cửa hàng</span>
        </a>
        <a href="#" class="flex flex-col items-center py-2 text-gray-500">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-heart-line text-lg"></i>
          </div>
          <span class="text-xs mt-1">Yêu thích</span>
        </a>
        <a href="#" class="flex flex-col items-center py-2 text-gray-500">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-user-line text-lg"></i>
          </div>
          <span class="text-xs mt-1">Tài khoản</span>
        </a>
      </div>
    </nav>

    <!-- JavaScript -->
    <script src="/static/js/main.js"></script>
  </body>
</html>
