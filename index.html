<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ShopSocial - <PERSON><PERSON> & <PERSON><PERSON><PERSON>i</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#007AFF", secondary: "#666666" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
          font-family: 'Inter', sans-serif;
          background-color: #FFFFFF;
      }
      .product-card:hover .quick-actions {
          opacity: 1;
      }
      .custom-checkbox {
          position: relative;
          cursor: pointer;
      }
      .custom-checkbox input {
          position: absolute;
          opacity: 0;
          cursor: pointer;
      }
      .checkmark {
          position: absolute;
          top: 0;
          left: 0;
          height: 20px;
          width: 20px;
          background-color: #fff;
          border: 1px solid #ddd;
          border-radius: 4px;
      }
      .custom-checkbox input:checked ~ .checkmark {
          background-color: #007AFF;
          border-color: #007AFF;
      }
      .checkmark:after {
          content: "";
          position: absolute;
          display: none;
      }
      .custom-checkbox input:checked ~ .checkmark:after {
          display: block;
      }
      .custom-checkbox .checkmark:after {
          left: 7px;
          top: 3px;
          width: 6px;
          height: 12px;
          border: solid white;
          border-width: 0 2px 2px 0;
          transform: rotate(45deg);
      }
      .switch {
          position: relative;
          display: inline-block;
          width: 48px;
          height: 24px;
      }
      .switch input {
          opacity: 0;
          width: 0;
          height: 0;
      }
      .slider {
          position: absolute;
          cursor: pointer;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: #ccc;
          transition: .4s;
          border-radius: 24px;
      }
      .slider:before {
          position: absolute;
          content: "";
          height: 18px;
          width: 18px;
          left: 3px;
          bottom: 3px;
          background-color: white;
          transition: .4s;
          border-radius: 50%;
      }
      input:checked + .slider {
          background-color: #007AFF;
      }
      input:checked + .slider:before {
          transform: translateX(24px);
      }
      input[type="range"] {
          -webkit-appearance: none;
          width: 100%;
          height: 6px;
          background: #e5e7eb;
          border-radius: 5px;
          outline: none;
      }
      input[type="range"]::-webkit-slider-thumb {
          -webkit-appearance: none;
          appearance: none;
          width: 18px;
          height: 18px;
          background: #007AFF;
          border-radius: 50%;
          cursor: pointer;
      }
      input[type="range"]::-moz-range-thumb {
          width: 18px;
          height: 18px;
          background: #007AFF;
          border-radius: 50%;
          cursor: pointer;
      }
      input[type="number"]::-webkit-inner-spin-button,
      input[type="number"]::-webkit-outer-spin-button {
          -webkit-appearance: none;
          margin: 0;
      }
      .search-input:focus {
          outline: none;
      }
      .notification-dot {
          position: absolute;
          top: -2px;
          right: -2px;
          width: 8px;
          height: 8px;
          background-color: #FF3B30;
          border-radius: 50%;
      }
    </style>
  </head>
  <body class="min-h-screen">
    <!-- Header -->
    <header class="sticky top-0 z-50 bg-white border-b border-gray-200">
      <div class="container mx-auto px-4 py-3">
        <div class="flex items-center justify-between">
          <!-- Logo -->
          <div class="flex items-center">
            <a href="#" class="text-2xl font-['Pacifico'] text-primary">logo</a>
          </div>

          <!-- Search Bar -->
          <div class="hidden md:flex items-center flex-1 max-w-xl mx-6">
            <div class="relative w-full">
              <input
                type="text"
                class="search-input w-full py-2 pl-10 pr-4 bg-gray-100 rounded-full text-sm"
                placeholder="Tìm kiếm sản phẩm, thương hiệu, cửa hàng..."
              />
              <div
                class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 flex items-center justify-center text-gray-400"
              >
                <i class="ri-search-line"></i>
              </div>
            </div>
          </div>

          <!-- Navigation Icons -->
          <div class="flex items-center space-x-5">
            <div class="md:hidden w-8 h-8 flex items-center justify-center">
              <i class="ri-search-line text-lg"></i>
            </div>

            <div class="relative w-8 h-8 flex items-center justify-center">
              <i class="ri-shopping-cart-2-line text-lg"></i>
              <span
                class="absolute -top-1 -right-1 bg-primary text-white text-xs w-5 h-5 flex items-center justify-center rounded-full"
                >3</span
              >
            </div>

            <div class="relative w-8 h-8 flex items-center justify-center">
              <i class="ri-notification-3-line text-lg"></i>
              <span class="notification-dot"></span>
            </div>

            <div class="relative w-8 h-8 flex items-center justify-center">
              <i class="ri-message-3-line text-lg"></i>
              <span class="notification-dot"></span>
            </div>

            <div class="relative">
              <img
                src="https://readdy.ai/api/search-image?query=young%20asian%20vietnamese%20woman%20professional%20headshot%20portrait%2C%20minimalist%20background%2C%20professional%20looking&width=100&height=100&seq=avatar1&orientation=squarish"
                alt="Avatar"
                class="w-8 h-8 rounded-full object-cover"
              />
            </div>
          </div>
        </div>

        <!-- Categories Navigation -->
        <nav
          class="hidden md:flex items-center space-x-8 mt-3 text-sm font-medium"
        >
          <a href="#" class="text-primary border-b-2 border-primary pb-2"
            >Trang chủ</a
          >
          <a href="#" class="text-gray-600 hover:text-primary">Thời trang</a>
          <a href="#" class="text-gray-600 hover:text-primary">Điện tử</a>
          <a href="#" class="text-gray-600 hover:text-primary">Mỹ phẩm</a>
          <a href="#" class="text-gray-600 hover:text-primary">Nhà cửa</a>
          <a href="#" class="text-gray-600 hover:text-primary">Sức khỏe</a>
          <a href="#" class="text-gray-600 hover:text-primary">Thể thao</a>
          <a href="#" class="text-gray-600 hover:text-primary">Sách</a>
          <a href="#" class="text-gray-600 hover:text-primary">Khuyến mãi</a>
        </nav>
      </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-6">
      <!-- Stories/Highlights -->
      <div class="mb-8">
        <div class="flex space-x-4 overflow-x-auto pb-2 scrollbar-hide">
          <div class="flex flex-col items-center space-y-1 flex-shrink-0">
            <div
              class="w-16 h-16 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 p-0.5"
            >
              <div
                class="w-full h-full rounded-full border-2 border-white flex items-center justify-center bg-white"
              >
                <i class="ri-add-line text-xl text-primary"></i>
              </div>
            </div>
            <span class="text-xs">Thêm mới</span>
          </div>

          <div class="flex flex-col items-center space-y-1 flex-shrink-0">
            <div
              class="w-16 h-16 rounded-full bg-gradient-to-r from-yellow-500 to-red-500 p-0.5"
            >
              <img
                src="https://readdy.ai/api/search-image?query=fashion%20clothing%20items%2C%20minimalist%20display%2C%20clean%20background&width=100&height=100&seq=story1&orientation=squarish"
                alt="Story"
                class="w-full h-full rounded-full border-2 border-white object-cover"
              />
            </div>
            <span class="text-xs">Thời trang</span>
          </div>

          <div class="flex flex-col items-center space-y-1 flex-shrink-0">
            <div
              class="w-16 h-16 rounded-full bg-gradient-to-r from-blue-500 to-teal-500 p-0.5"
            >
              <img
                src="https://readdy.ai/api/search-image?query=electronics%20gadgets%2C%20minimalist%20display%2C%20clean%20background&width=100&height=100&seq=story2&orientation=squarish"
                alt="Story"
                class="w-full h-full rounded-full border-2 border-white object-cover"
              />
            </div>
            <span class="text-xs">Điện tử</span>
          </div>

          <div class="flex flex-col items-center space-y-1 flex-shrink-0">
            <div
              class="w-16 h-16 rounded-full bg-gradient-to-r from-pink-500 to-purple-500 p-0.5"
            >
              <img
                src="https://readdy.ai/api/search-image?query=beauty%20cosmetics%20products%2C%20minimalist%20display%2C%20clean%20background&width=100&height=100&seq=story3&orientation=squarish"
                alt="Story"
                class="w-full h-full rounded-full border-2 border-white object-cover"
              />
            </div>
            <span class="text-xs">Mỹ phẩm</span>
          </div>

          <div class="flex flex-col items-center space-y-1 flex-shrink-0">
            <div
              class="w-16 h-16 rounded-full bg-gradient-to-r from-green-500 to-teal-500 p-0.5"
            >
              <img
                src="https://readdy.ai/api/search-image?query=home%20decor%20items%2C%20minimalist%20display%2C%20clean%20background&width=100&height=100&seq=story4&orientation=squarish"
                alt="Story"
                class="w-full h-full rounded-full border-2 border-white object-cover"
              />
            </div>
            <span class="text-xs">Nhà cửa</span>
          </div>

          <div class="flex flex-col items-center space-y-1 flex-shrink-0">
            <div
              class="w-16 h-16 rounded-full bg-gradient-to-r from-red-500 to-orange-500 p-0.5"
            >
              <img
                src="https://readdy.ai/api/search-image?query=sports%20equipment%2C%20minimalist%20display%2C%20clean%20background&width=100&height=100&seq=story5&orientation=squarish"
                alt="Story"
                class="w-full h-full rounded-full border-2 border-white object-cover"
              />
            </div>
            <span class="text-xs">Thể thao</span>
          </div>

          <div class="flex flex-col items-center space-y-1 flex-shrink-0">
            <div
              class="w-16 h-16 rounded-full bg-gradient-to-r from-indigo-500 to-blue-500 p-0.5"
            >
              <img
                src="https://readdy.ai/api/search-image?query=books%20and%20stationery%2C%20minimalist%20display%2C%20clean%20background&width=100&height=100&seq=story6&orientation=squarish"
                alt="Story"
                class="w-full h-full rounded-full border-2 border-white object-cover"
              />
            </div>
            <span class="text-xs">Sách</span>
          </div>
        </div>
      </div>

      <!-- Flash Sale Banner -->
      <div
        class="mb-8 bg-gradient-to-r from-red-500 to-orange-500 rounded-lg overflow-hidden"
      >
        <div class="flex flex-col md:flex-row">
          <div class="p-6 md:p-8 flex-1 text-white">
            <h2 class="text-2xl md:text-3xl font-bold mb-2">
              Flash Sale Cuối Tuần
            </h2>
            <p class="mb-4 text-white/80">
              Giảm đến 70% cho hàng ngàn sản phẩm
            </p>
            <div class="flex space-x-4 mb-6">
              <div class="bg-white/20 rounded p-2 text-center">
                <div class="text-xl font-bold">12</div>
                <div class="text-xs">Giờ</div>
              </div>
              <div class="bg-white/20 rounded p-2 text-center">
                <div class="text-xl font-bold">45</div>
                <div class="text-xs">Phút</div>
              </div>
              <div class="bg-white/20 rounded p-2 text-center">
                <div class="text-xl font-bold">30</div>
                <div class="text-xs">Giây</div>
              </div>
            </div>
            <button
              class="bg-white text-red-500 px-6 py-2 rounded-button font-medium hover:bg-white/90 whitespace-nowrap"
            >
              Mua ngay
            </button>
          </div>
          <div class="md:w-2/5">
            <img
              src="https://readdy.ai/api/search-image?query=shopping%20sale%20promotion%20with%20various%20products%20on%20display%2C%20vibrant%20colorful%20background%2C%20professional%20product%20photography&width=600&height=400&seq=flashsale&orientation=landscape"
              alt="Flash Sale"
              class="w-full h-full object-cover object-top"
            />
          </div>
        </div>
      </div>

      <!-- Newsfeed -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Post 1 -->
        <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <!-- Post Header -->
          <div class="p-4 flex justify-between items-center">
            <div class="flex items-center space-x-3">
              <img
                src="https://readdy.ai/api/search-image?query=vietnamese%20fashion%20boutique%20logo%2C%20minimalist%20design&width=50&height=50&seq=shop1&orientation=squarish"
                alt="Shop Avatar"
                class="w-10 h-10 rounded-full object-cover"
              />
              <div>
                <h3 class="font-medium text-sm">Boutique Hà Nội</h3>
                <p class="text-xs text-gray-500">Cửa hàng chính thức</p>
              </div>
            </div>
            <button class="text-primary text-sm font-medium whitespace-nowrap">
              Theo dõi
            </button>
          </div>

          <!-- Post Image -->
          <div class="relative">
            <img
              src="https://readdy.ai/api/search-image?query=elegant%20vietnamese%20woman%20wearing%20modern%20ao%20dai%20dress%2C%20fashion%20photography%2C%20clean%20white%20background%2C%20professional%20product%20photography&width=600&height=600&seq=post1&orientation=squarish"
              alt="Product"
              class="w-full h-80 object-cover object-top"
            />

            <!-- Quick Actions -->
            <div
              class="quick-actions opacity-0 transition-opacity absolute inset-0 bg-black/30 flex items-center justify-center space-x-4"
            >
              <button
                class="bg-white w-10 h-10 rounded-full flex items-center justify-center"
              >
                <i class="ri-heart-line text-lg"></i>
              </button>
              <button
                class="bg-white w-10 h-10 rounded-full flex items-center justify-center"
              >
                <i class="ri-shopping-cart-2-line text-lg"></i>
              </button>
              <button
                class="bg-white w-10 h-10 rounded-full flex items-center justify-center"
              >
                <i class="ri-share-line text-lg"></i>
              </button>
            </div>
          </div>

          <!-- Post Actions -->
          <div class="p-4">
            <div class="flex justify-between items-center mb-3">
              <div class="flex space-x-4">
                <button class="flex items-center space-x-1">
                  <i class="ri-heart-line text-lg"></i>
                  <span class="text-sm">245</span>
                </button>
                <button class="flex items-center space-x-1">
                  <i class="ri-chat-1-line text-lg"></i>
                  <span class="text-sm">42</span>
                </button>
              </div>
              <button>
                <i class="ri-bookmark-line text-lg"></i>
              </button>
            </div>

            <!-- Product Info -->
            <h3 class="font-medium mb-1">Áo Dài Hiện Đại Phối Hoa</h3>
            <div class="flex items-center space-x-2 mb-2">
              <span class="text-lg font-bold text-primary">1.290.000₫</span>
              <span class="text-sm text-gray-500 line-through">1.590.000₫</span>
              <span class="text-xs bg-red-100 text-red-600 px-2 py-0.5 rounded"
                >-19%</span
              >
            </div>
            <div class="flex items-center space-x-1 mb-3">
              <div class="flex">
                <i class="ri-star-fill text-yellow-400"></i>
                <i class="ri-star-fill text-yellow-400"></i>
                <i class="ri-star-fill text-yellow-400"></i>
                <i class="ri-star-fill text-yellow-400"></i>
                <i class="ri-star-half-fill text-yellow-400"></i>
              </div>
              <span class="text-xs text-gray-500">(126)</span>
            </div>
            <p class="text-sm text-gray-600 mb-4">
              Áo dài hiện đại với họa tiết hoa tinh tế, phù hợp cho các dịp đặc
              biệt và lễ hội.
            </p>
            <div class="flex space-x-2">
              <button
                class="flex-1 bg-gray-100 text-gray-800 py-2 rounded-button font-medium hover:bg-gray-200 whitespace-nowrap"
              >
                <i class="ri-shopping-cart-2-line mr-1"></i> Thêm vào giỏ
              </button>
              <button
                class="flex-1 bg-primary text-white py-2 rounded-button font-medium hover:bg-primary/90 whitespace-nowrap"
              >
                Mua ngay
              </button>
            </div>
          </div>
        </div>

        <!-- Post 2 -->
        <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <!-- Post Header -->
          <div class="p-4 flex justify-between items-center">
            <div class="flex items-center space-x-3">
              <img
                src="https://readdy.ai/api/search-image?query=vietnamese%20tech%20store%20logo%2C%20modern%20design&width=50&height=50&seq=shop2&orientation=squarish"
                alt="Shop Avatar"
                class="w-10 h-10 rounded-full object-cover"
              />
              <div>
                <h3 class="font-medium text-sm">TechZone Việt</h3>
                <p class="text-xs text-gray-500">Đại lý ủy quyền</p>
              </div>
            </div>
            <button class="text-primary text-sm font-medium whitespace-nowrap">
              Theo dõi
            </button>
          </div>

          <!-- Post Image -->
          <div class="relative">
            <img
              src="https://readdy.ai/api/search-image?query=modern%20smartphone%20on%20clean%20white%20background%2C%20professional%20product%20photography%2C%20high%20resolution&width=600&height=600&seq=post2&orientation=squarish"
              alt="Product"
              class="w-full h-80 object-cover object-top"
            />

            <!-- Quick Actions -->
            <div
              class="quick-actions opacity-0 transition-opacity absolute inset-0 bg-black/30 flex items-center justify-center space-x-4"
            >
              <button
                class="bg-white w-10 h-10 rounded-full flex items-center justify-center"
              >
                <i class="ri-heart-line text-lg"></i>
              </button>
              <button
                class="bg-white w-10 h-10 rounded-full flex items-center justify-center"
              >
                <i class="ri-shopping-cart-2-line text-lg"></i>
              </button>
              <button
                class="bg-white w-10 h-10 rounded-full flex items-center justify-center"
              >
                <i class="ri-share-line text-lg"></i>
              </button>
            </div>
          </div>

          <!-- Post Actions -->
          <div class="p-4">
            <div class="flex justify-between items-center mb-3">
              <div class="flex space-x-4">
                <button class="flex items-center space-x-1">
                  <i class="ri-heart-line text-lg"></i>
                  <span class="text-sm">389</span>
                </button>
                <button class="flex items-center space-x-1">
                  <i class="ri-chat-1-line text-lg"></i>
                  <span class="text-sm">76</span>
                </button>
              </div>
              <button>
                <i class="ri-bookmark-line text-lg"></i>
              </button>
            </div>

            <!-- Product Info -->
            <h3 class="font-medium mb-1">Điện thoại SmartX Pro 2025</h3>
            <div class="flex items-center space-x-2 mb-2">
              <span class="text-lg font-bold text-primary">18.990.000₫</span>
              <span class="text-sm text-gray-500 line-through"
                >21.990.000₫</span
              >
              <span class="text-xs bg-red-100 text-red-600 px-2 py-0.5 rounded"
                >-14%</span
              >
            </div>
            <div class="flex items-center space-x-1 mb-3">
              <div class="flex">
                <i class="ri-star-fill text-yellow-400"></i>
                <i class="ri-star-fill text-yellow-400"></i>
                <i class="ri-star-fill text-yellow-400"></i>
                <i class="ri-star-fill text-yellow-400"></i>
                <i class="ri-star-fill text-yellow-400"></i>
              </div>
              <span class="text-xs text-gray-500">(352)</span>
            </div>
            <p class="text-sm text-gray-600 mb-4">
              Điện thoại thông minh mới nhất với camera 108MP, màn hình AMOLED
              6.7" và pin 5000mAh.
            </p>
            <div class="flex space-x-2">
              <button
                class="flex-1 bg-gray-100 text-gray-800 py-2 rounded-button font-medium hover:bg-gray-200 whitespace-nowrap"
              >
                <i class="ri-shopping-cart-2-line mr-1"></i> Thêm vào giỏ
              </button>
              <button
                class="flex-1 bg-primary text-white py-2 rounded-button font-medium hover:bg-primary/90 whitespace-nowrap"
              >
                Mua ngay
              </button>
            </div>
          </div>
        </div>

        <!-- Post 3 -->
        <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <!-- Post Header -->
          <div class="p-4 flex justify-between items-center">
            <div class="flex items-center space-x-3">
              <img
                src="https://readdy.ai/api/search-image?query=vietnamese%20beauty%20brand%20logo%2C%20elegant%20design&width=50&height=50&seq=shop3&orientation=squarish"
                alt="Shop Avatar"
                class="w-10 h-10 rounded-full object-cover"
              />
              <div>
                <h3 class="font-medium text-sm">BeautyViet Official</h3>
                <p class="text-xs text-gray-500">Thương hiệu nội địa</p>
              </div>
            </div>
            <button
              class="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-medium whitespace-nowrap"
            >
              Đang theo dõi
            </button>
          </div>

          <!-- Post Image -->
          <div class="relative">
            <img
              src="https://readdy.ai/api/search-image?query=luxury%20skincare%20products%20set%20on%20marble%20background%2C%20professional%20beauty%20product%20photography%2C%20clean%20minimal%20aesthetic&width=600&height=600&seq=post3&orientation=squarish"
              alt="Product"
              class="w-full h-80 object-cover object-top"
            />

            <!-- Quick Actions -->
            <div
              class="quick-actions opacity-0 transition-opacity absolute inset-0 bg-black/30 flex items-center justify-center space-x-4"
            >
              <button
                class="bg-white w-10 h-10 rounded-full flex items-center justify-center"
              >
                <i class="ri-heart-fill text-red-500 text-lg"></i>
              </button>
              <button
                class="bg-white w-10 h-10 rounded-full flex items-center justify-center"
              >
                <i class="ri-shopping-cart-2-line text-lg"></i>
              </button>
              <button
                class="bg-white w-10 h-10 rounded-full flex items-center justify-center"
              >
                <i class="ri-share-line text-lg"></i>
              </button>
            </div>
          </div>

          <!-- Post Actions -->
          <div class="p-4">
            <div class="flex justify-between items-center mb-3">
              <div class="flex space-x-4">
                <button class="flex items-center space-x-1">
                  <i class="ri-heart-fill text-red-500 text-lg"></i>
                  <span class="text-sm">512</span>
                </button>
                <button class="flex items-center space-x-1">
                  <i class="ri-chat-1-line text-lg"></i>
                  <span class="text-sm">94</span>
                </button>
              </div>
              <button>
                <i class="ri-bookmark-fill text-primary text-lg"></i>
              </button>
            </div>

            <!-- Product Info -->
            <h3 class="font-medium mb-1">Bộ Dưỡng Da Cao Cấp Trà Xanh</h3>
            <div class="flex items-center space-x-2 mb-2">
              <span class="text-lg font-bold text-primary">890.000₫</span>
              <span class="text-sm text-gray-500 line-through">1.290.000₫</span>
              <span class="text-xs bg-red-100 text-red-600 px-2 py-0.5 rounded"
                >-31%</span
              >
            </div>
            <div class="flex items-center space-x-1 mb-3">
              <div class="flex">
                <i class="ri-star-fill text-yellow-400"></i>
                <i class="ri-star-fill text-yellow-400"></i>
                <i class="ri-star-fill text-yellow-400"></i>
                <i class="ri-star-fill text-yellow-400"></i>
                <i class="ri-star-fill text-yellow-400"></i>
              </div>
              <span class="text-xs text-gray-500">(218)</span>
            </div>
            <p class="text-sm text-gray-600 mb-4">
              Bộ sản phẩm dưỡng da chiết xuất từ trà xanh Đà Lạt, giúp làm sạch,
              dưỡng ẩm và chống lão hóa.
            </p>
            <div class="flex space-x-2">
              <button
                class="flex-1 bg-gray-100 text-gray-800 py-2 rounded-button font-medium hover:bg-gray-200 whitespace-nowrap"
              >
                <i class="ri-shopping-cart-2-line mr-1"></i> Thêm vào giỏ
              </button>
              <button
                class="flex-1 bg-primary text-white py-2 rounded-button font-medium hover:bg-primary/90 whitespace-nowrap"
              >
                Mua ngay
              </button>
            </div>
          </div>
        </div>

        <!-- Post 4 -->
        <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <!-- Post Header -->
          <div class="p-4 flex justify-between items-center">
            <div class="flex items-center space-x-3">
              <img
                src="https://readdy.ai/api/search-image?query=vietnamese%20home%20decor%20brand%20logo%2C%20modern%20design&width=50&height=50&seq=shop4&orientation=squarish"
                alt="Shop Avatar"
                class="w-10 h-10 rounded-full object-cover"
              />
              <div>
                <h3 class="font-medium text-sm">Nhà Xinh Decor</h3>
                <p class="text-xs text-gray-500">Nội thất & Trang trí</p>
              </div>
            </div>
            <button class="text-primary text-sm font-medium whitespace-nowrap">
              Theo dõi
            </button>
          </div>

          <!-- Post Image -->
          <div class="relative">
            <img
              src="https://readdy.ai/api/search-image?query=modern%20minimalist%20home%20decor%20set%20with%20vases%20and%20decorative%20objects%2C%20clean%20white%20background%2C%20professional%20product%20photography&width=600&height=600&seq=post4&orientation=squarish"
              alt="Product"
              class="w-full h-80 object-cover object-top"
            />

            <!-- Quick Actions -->
            <div
              class="quick-actions opacity-0 transition-opacity absolute inset-0 bg-black/30 flex items-center justify-center space-x-4"
            >
              <button
                class="bg-white w-10 h-10 rounded-full flex items-center justify-center"
              >
                <i class="ri-heart-line text-lg"></i>
              </button>
              <button
                class="bg-white w-10 h-10 rounded-full flex items-center justify-center"
              >
                <i class="ri-shopping-cart-2-line text-lg"></i>
              </button>
              <button
                class="bg-white w-10 h-10 rounded-full flex items-center justify-center"
              >
                <i class="ri-share-line text-lg"></i>
              </button>
            </div>
          </div>

          <!-- Post Actions -->
          <div class="p-4">
            <div class="flex justify-between items-center mb-3">
              <div class="flex space-x-4">
                <button class="flex items-center space-x-1">
                  <i class="ri-heart-line text-lg"></i>
                  <span class="text-sm">178</span>
                </button>
                <button class="flex items-center space-x-1">
                  <i class="ri-chat-1-line text-lg"></i>
                  <span class="text-sm">32</span>
                </button>
              </div>
              <button>
                <i class="ri-bookmark-line text-lg"></i>
              </button>
            </div>

            <!-- Product Info -->
            <h3 class="font-medium mb-1">Bộ Bình Gốm Trang Trí Scandinavian</h3>
            <div class="flex items-center space-x-2 mb-2">
              <span class="text-lg font-bold text-primary">650.000₫</span>
              <span class="text-sm text-gray-500 line-through">850.000₫</span>
              <span class="text-xs bg-red-100 text-red-600 px-2 py-0.5 rounded"
                >-24%</span
              >
            </div>
            <div class="flex items-center space-x-1 mb-3">
              <div class="flex">
                <i class="ri-star-fill text-yellow-400"></i>
                <i class="ri-star-fill text-yellow-400"></i>
                <i class="ri-star-fill text-yellow-400"></i>
                <i class="ri-star-fill text-yellow-400"></i>
                <i class="ri-star-line text-yellow-400"></i>
              </div>
              <span class="text-xs text-gray-500">(87)</span>
            </div>
            <p class="text-sm text-gray-600 mb-4">
              Bộ bình gốm trang trí phong cách Bắc Âu, làm thủ công từ gốm cao
              cấp, phù hợp với mọi không gian.
            </p>
            <div class="flex space-x-2">
              <button
                class="flex-1 bg-gray-100 text-gray-800 py-2 rounded-button font-medium hover:bg-gray-200 whitespace-nowrap"
              >
                <i class="ri-shopping-cart-2-line mr-1"></i> Thêm vào giỏ
              </button>
              <button
                class="flex-1 bg-primary text-white py-2 rounded-button font-medium hover:bg-primary/90 whitespace-nowrap"
              >
                Mua ngay
              </button>
            </div>
          </div>
        </div>

        <!-- Post 5 -->
        <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <!-- Post Header -->
          <div class="p-4 flex justify-between items-center">
            <div class="flex items-center space-x-3">
              <img
                src="https://readdy.ai/api/search-image?query=vietnamese%20sports%20brand%20logo%2C%20dynamic%20design&width=50&height=50&seq=shop5&orientation=squarish"
                alt="Shop Avatar"
                class="w-10 h-10 rounded-full object-cover"
              />
              <div>
                <h3 class="font-medium text-sm">SportLife Việt Nam</h3>
                <p class="text-xs text-gray-500">Đồ thể thao chính hãng</p>
              </div>
            </div>
            <button class="text-primary text-sm font-medium whitespace-nowrap">
              Theo dõi
            </button>
          </div>

          <!-- Post Image -->
          <div class="relative">
            <img
              src="https://readdy.ai/api/search-image?query=modern%20athletic%20shoes%20on%20clean%20white%20background%2C%20professional%20product%20photography%2C%20high%20resolution&width=600&height=600&seq=post5&orientation=squarish"
              alt="Product"
              class="w-full h-80 object-cover object-top"
            />

            <!-- Quick Actions -->
            <div
              class="quick-actions opacity-0 transition-opacity absolute inset-0 bg-black/30 flex items-center justify-center space-x-4"
            >
              <button
                class="bg-white w-10 h-10 rounded-full flex items-center justify-center"
              >
                <i class="ri-heart-line text-lg"></i>
              </button>
              <button
                class="bg-white w-10 h-10 rounded-full flex items-center justify-center"
              >
                <i class="ri-shopping-cart-2-line text-lg"></i>
              </button>
              <button
                class="bg-white w-10 h-10 rounded-full flex items-center justify-center"
              >
                <i class="ri-share-line text-lg"></i>
              </button>
            </div>
          </div>

          <!-- Post Actions -->
          <div class="p-4">
            <div class="flex justify-between items-center mb-3">
              <div class="flex space-x-4">
                <button class="flex items-center space-x-1">
                  <i class="ri-heart-line text-lg"></i>
                  <span class="text-sm">312</span>
                </button>
                <button class="flex items-center space-x-1">
                  <i class="ri-chat-1-line text-lg"></i>
                  <span class="text-sm">58</span>
                </button>
              </div>
              <button>
                <i class="ri-bookmark-line text-lg"></i>
              </button>
            </div>

            <!-- Product Info -->
            <h3 class="font-medium mb-1">Giày Chạy Bộ UltraBoost X9</h3>
            <div class="flex items-center space-x-2 mb-2">
              <span class="text-lg font-bold text-primary">2.790.000₫</span>
              <span class="text-sm text-gray-500 line-through">3.490.000₫</span>
              <span class="text-xs bg-red-100 text-red-600 px-2 py-0.5 rounded"
                >-20%</span
              >
            </div>
            <div class="flex items-center space-x-1 mb-3">
              <div class="flex">
                <i class="ri-star-fill text-yellow-400"></i>
                <i class="ri-star-fill text-yellow-400"></i>
                <i class="ri-star-fill text-yellow-400"></i>
                <i class="ri-star-fill text-yellow-400"></i>
                <i class="ri-star-half-fill text-yellow-400"></i>
              </div>
              <span class="text-xs text-gray-500">(176)</span>
            </div>
            <p class="text-sm text-gray-600 mb-4">
              Giày chạy bộ công nghệ đệm cao cấp, nhẹ và thoáng khí, phù hợp cho
              cả tập luyện và đi hàng ngày.
            </p>
            <div class="flex space-x-2">
              <button
                class="flex-1 bg-gray-100 text-gray-800 py-2 rounded-button font-medium hover:bg-gray-200 whitespace-nowrap"
              >
                <i class="ri-shopping-cart-2-line mr-1"></i> Thêm vào giỏ
              </button>
              <button
                class="flex-1 bg-primary text-white py-2 rounded-button font-medium hover:bg-primary/90 whitespace-nowrap"
              >
                Mua ngay
              </button>
            </div>
          </div>
        </div>

        <!-- Post 6 -->
        <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <!-- Post Header -->
          <div class="p-4 flex justify-between items-center">
            <div class="flex items-center space-x-3">
              <img
                src="https://readdy.ai/api/search-image?query=vietnamese%20book%20store%20logo%2C%20elegant%20design&width=50&height=50&seq=shop6&orientation=squarish"
                alt="Shop Avatar"
                class="w-10 h-10 rounded-full object-cover"
              />
              <div>
                <h3 class="font-medium text-sm">Nhã Nam Books</h3>
                <p class="text-xs text-gray-500">Nhà xuất bản chính thức</p>
              </div>
            </div>
            <button
              class="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-medium whitespace-nowrap"
            >
              Đang theo dõi
            </button>
          </div>

          <!-- Post Image -->
          <div class="relative">
            <img
              src="https://readdy.ai/api/search-image?query=stack%20of%20books%20with%20coffee%20cup%20on%20clean%20white%20background%2C%20professional%20product%20photography%2C%20high%20resolution&width=600&height=600&seq=post6&orientation=squarish"
              alt="Product"
              class="w-full h-80 object-cover object-top"
            />

            <!-- Quick Actions -->
            <div
              class="quick-actions opacity-0 transition-opacity absolute inset-0 bg-black/30 flex items-center justify-center space-x-4"
            >
              <button
                class="bg-white w-10 h-10 rounded-full flex items-center justify-center"
              >
                <i class="ri-heart-fill text-red-500 text-lg"></i>
              </button>
              <button
                class="bg-white w-10 h-10 rounded-full flex items-center justify-center"
              >
                <i class="ri-shopping-cart-2-line text-lg"></i>
              </button>
              <button
                class="bg-white w-10 h-10 rounded-full flex items-center justify-center"
              >
                <i class="ri-share-line text-lg"></i>
              </button>
            </div>
          </div>

          <!-- Post Actions -->
          <div class="p-4">
            <div class="flex justify-between items-center mb-3">
              <div class="flex space-x-4">
                <button class="flex items-center space-x-1">
                  <i class="ri-heart-fill text-red-500 text-lg"></i>
                  <span class="text-sm">287</span>
                </button>
                <button class="flex items-center space-x-1">
                  <i class="ri-chat-1-line text-lg"></i>
                  <span class="text-sm">64</span>
                </button>
              </div>
              <button>
                <i class="ri-bookmark-fill text-primary text-lg"></i>
              </button>
            </div>

            <!-- Product Info -->
            <h3 class="font-medium mb-1">Bộ Sách Văn Học Việt Nam Hiện Đại</h3>
            <div class="flex items-center space-x-2 mb-2">
              <span class="text-lg font-bold text-primary">450.000₫</span>
              <span class="text-sm text-gray-500 line-through">650.000₫</span>
              <span class="text-xs bg-red-100 text-red-600 px-2 py-0.5 rounded"
                >-31%</span
              >
            </div>
            <div class="flex items-center space-x-1 mb-3">
              <div class="flex">
                <i class="ri-star-fill text-yellow-400"></i>
                <i class="ri-star-fill text-yellow-400"></i>
                <i class="ri-star-fill text-yellow-400"></i>
                <i class="ri-star-fill text-yellow-400"></i>
                <i class="ri-star-fill text-yellow-400"></i>
              </div>
              <span class="text-xs text-gray-500">(142)</span>
            </div>
            <p class="text-sm text-gray-600 mb-4">
              Bộ 5 cuốn sách văn học Việt Nam hiện đại từ các tác giả nổi tiếng,
              bản in đặc biệt kỷ niệm.
            </p>
            <div class="flex space-x-2">
              <button
                class="flex-1 bg-gray-100 text-gray-800 py-2 rounded-button font-medium hover:bg-gray-200 whitespace-nowrap"
              >
                <i class="ri-shopping-cart-2-line mr-1"></i> Thêm vào giỏ
              </button>
              <button
                class="flex-1 bg-primary text-white py-2 rounded-button font-medium hover:bg-primary/90 whitespace-nowrap"
              >
                Mua ngay
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Load More Button -->
      <div class="mt-8 text-center">
        <button
          class="bg-white border border-gray-300 text-gray-700 px-6 py-3 rounded-button font-medium hover:bg-gray-50 whitespace-nowrap"
        >
          <i class="ri-refresh-line mr-2"></i> Xem thêm
        </button>
      </div>
    </main>

    <!-- Chat Floating Button -->
    <div class="fixed bottom-20 right-6 z-40 md:bottom-6">
      <button
        class="w-12 h-12 bg-primary text-white rounded-full shadow-lg flex items-center justify-center"
      >
        <i class="ri-message-3-line text-xl"></i>
      </button>
    </div>

    <!-- Mobile Navigation -->
    <nav
      class="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-40"
    >
      <div class="flex justify-around">
        <a href="#" class="flex flex-col items-center py-2 text-primary">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-home-5-fill text-lg"></i>
          </div>
          <span class="text-xs mt-1">Trang chủ</span>
        </a>
        <a href="#" class="flex flex-col items-center py-2 text-gray-500">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-search-line text-lg"></i>
          </div>
          <span class="text-xs mt-1">Tìm kiếm</span>
        </a>
        <a href="#" class="flex flex-col items-center py-2 text-gray-500">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-shopping-bag-line text-lg"></i>
          </div>
          <span class="text-xs mt-1">Cửa hàng</span>
        </a>
        <a href="#" class="flex flex-col items-center py-2 text-gray-500">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-heart-line text-lg"></i>
          </div>
          <span class="text-xs mt-1">Yêu thích</span>
        </a>
        <a href="#" class="flex flex-col items-center py-2 text-gray-500">
          <div class="w-6 h-6 flex items-center justify-center">
            <i class="ri-user-line text-lg"></i>
          </div>
          <span class="text-xs mt-1">Tài khoản</span>
        </a>
      </div>
    </nav>

    <!-- Product Quick View Modal -->
    <div id="quickViewModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
      <div class="flex items-center justify-center min-h-screen p-4">
        <div
          class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          id="modalOverlay"
        ></div>

        <div
          class="relative bg-white rounded-lg max-w-4xl w-full mx-auto shadow-xl overflow-hidden"
        >
          <div class="absolute top-4 right-4 z-10">
            <button id="closeModal" class="bg-white rounded-full p-1 shadow-md">
              <i class="ri-close-line text-xl"></i>
            </button>
          </div>

          <div class="flex flex-col md:flex-row">
            <!-- Product Images -->
            <div class="md:w-1/2 bg-gray-100">
              <div class="relative h-96">
                <img
                  src="https://readdy.ai/api/search-image?query=elegant%20vietnamese%20woman%20wearing%20modern%20ao%20dai%20dress%2C%20fashion%20photography%2C%20clean%20white%20background%2C%20professional%20product%20photography&width=600&height=600&seq=modal1&orientation=squarish"
                  alt="Product"
                  class="w-full h-full object-cover object-top"
                />
              </div>
              <div class="flex p-2 space-x-2">
                <div class="w-1/4 h-20 border-2 border-primary rounded">
                  <img
                    src="https://readdy.ai/api/search-image?query=elegant%20vietnamese%20woman%20wearing%20modern%20ao%20dai%20dress%2C%20fashion%20photography%2C%20clean%20white%20background%2C%20professional%20product%20photography&width=100&height=100&seq=thumb1&orientation=squarish"
                    alt="Thumbnail"
                    class="w-full h-full object-cover object-top"
                  />
                </div>
                <div class="w-1/4 h-20 border border-gray-200 rounded">
                  <img
                    src="https://readdy.ai/api/search-image?query=elegant%20vietnamese%20woman%20wearing%20modern%20ao%20dai%20dress%2C%20close-up%20detail%2C%20fashion%20photography%2C%20clean%20white%20background&width=100&height=100&seq=thumb2&orientation=squarish"
                    alt="Thumbnail"
                    class="w-full h-full object-cover object-top"
                  />
                </div>
                <div class="w-1/4 h-20 border border-gray-200 rounded">
                  <img
                    src="https://readdy.ai/api/search-image?query=elegant%20vietnamese%20woman%20wearing%20modern%20ao%20dai%20dress%2C%20side%20view%2C%20fashion%20photography%2C%20clean%20white%20background&width=100&height=100&seq=thumb3&orientation=squarish"
                    alt="Thumbnail"
                    class="w-full h-full object-cover object-top"
                  />
                </div>
                <div class="w-1/4 h-20 border border-gray-200 rounded">
                  <img
                    src="https://readdy.ai/api/search-image?query=elegant%20vietnamese%20woman%20wearing%20modern%20ao%20dai%20dress%2C%20back%20view%2C%20fashion%20photography%2C%20clean%20white%20background&width=100&height=100&seq=thumb4&orientation=squarish"
                    alt="Thumbnail"
                    class="w-full h-full object-cover object-top"
                  />
                </div>
              </div>
            </div>

            <!-- Product Info -->
            <div class="md:w-1/2 p-6">
              <div class="flex items-center space-x-2 mb-2">
                <span class="px-2 py-1 bg-red-100 text-red-600 text-xs rounded"
                  >Bán chạy</span
                >
                <span
                  class="px-2 py-1 bg-green-100 text-green-600 text-xs rounded"
                  >Còn hàng</span
                >
              </div>

              <h2 class="text-xl font-bold mb-2">Áo Dài Hiện Đại Phối Hoa</h2>

              <div class="flex items-center space-x-2 mb-4">
                <div class="flex">
                  <i class="ri-star-fill text-yellow-400"></i>
                  <i class="ri-star-fill text-yellow-400"></i>
                  <i class="ri-star-fill text-yellow-400"></i>
                  <i class="ri-star-fill text-yellow-400"></i>
                  <i class="ri-star-half-fill text-yellow-400"></i>
                </div>
                <span class="text-sm text-gray-500">(126 đánh giá)</span>
              </div>

              <div class="flex items-center space-x-2 mb-6">
                <span class="text-2xl font-bold text-primary">1.290.000₫</span>
                <span class="text-gray-500 line-through">1.590.000₫</span>
                <span class="bg-red-100 text-red-600 px-2 py-0.5 rounded"
                  >-19%</span
                >
              </div>

              <div class="mb-6">
                <h3 class="font-medium mb-2">Màu sắc:</h3>
                <div class="flex space-x-2">
                  <button
                    class="w-8 h-8 rounded-full bg-blue-500 border-2 border-white ring-2 ring-blue-500"
                  ></button>
                  <button class="w-8 h-8 rounded-full bg-red-500"></button>
                  <button class="w-8 h-8 rounded-full bg-green-500"></button>
                  <button class="w-8 h-8 rounded-full bg-purple-500"></button>
                </div>
              </div>

              <div class="mb-6">
                <h3 class="font-medium mb-2">Kích thước:</h3>
                <div class="flex flex-wrap gap-2">
                  <button
                    class="px-3 py-1 border border-gray-300 rounded text-sm"
                  >
                    S
                  </button>
                  <button
                    class="px-3 py-1 bg-primary text-white border border-primary rounded text-sm"
                  >
                    M
                  </button>
                  <button
                    class="px-3 py-1 border border-gray-300 rounded text-sm"
                  >
                    L
                  </button>
                  <button
                    class="px-3 py-1 border border-gray-300 rounded text-sm"
                  >
                    XL
                  </button>
                </div>
              </div>

              <div class="mb-6">
                <h3 class="font-medium mb-2">Số lượng:</h3>
                <div class="flex items-center">
                  <button
                    class="w-8 h-8 border border-gray-300 rounded-l flex items-center justify-center"
                  >
                    <i class="ri-subtract-line"></i>
                  </button>
                  <input
                    type="number"
                    value="1"
                    min="1"
                    class="w-12 h-8 border-t border-b border-gray-300 text-center text-sm"
                  />
                  <button
                    class="w-8 h-8 border border-gray-300 rounded-r flex items-center justify-center"
                  >
                    <i class="ri-add-line"></i>
                  </button>
                </div>
              </div>

              <div class="flex space-x-3 mb-6">
                <button
                  class="flex-1 bg-primary text-white py-3 rounded-button font-medium hover:bg-primary/90 whitespace-nowrap"
                >
                  Mua ngay
                </button>
                <button
                  class="flex-1 bg-gray-100 text-gray-800 py-3 rounded-button font-medium hover:bg-gray-200 whitespace-nowrap"
                >
                  <i class="ri-shopping-cart-2-line mr-1"></i> Thêm vào giỏ
                </button>
              </div>

              <div class="border-t border-gray-200 pt-4">
                <div class="flex items-center space-x-4 text-sm text-gray-600">
                  <div class="flex items-center">
                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                      <i class="ri-truck-line"></i>
                    </div>
                    <span>Miễn phí vận chuyển</span>
                  </div>
                  <div class="flex items-center">
                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                      <i class="ri-refresh-line"></i>
                    </div>
                    <span>Đổi trả trong 30 ngày</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Shopping Cart Sidebar -->
    <div
      id="cartSidebar"
      class="fixed inset-y-0 right-0 z-50 w-full md:w-96 bg-white shadow-xl transform translate-x-full transition-transform duration-300 ease-in-out"
    >
      <div class="flex flex-col h-full">
        <!-- Cart Header -->
        <div
          class="px-4 py-4 border-b border-gray-200 flex items-center justify-between"
        >
          <h2 class="text-lg font-bold">Giỏ hàng của bạn (3)</h2>
          <button
            id="closeCart"
            class="w-8 h-8 flex items-center justify-center"
          >
            <i class="ri-close-line text-xl"></i>
          </button>
        </div>

        <!-- Cart Items -->
        <div class="flex-1 overflow-y-auto p-4 space-y-4">
          <!-- Cart Item 1 -->
          <div class="flex border border-gray-200 rounded-lg overflow-hidden">
            <div class="w-24 h-24">
              <img
                src="https://readdy.ai/api/search-image?query=elegant%20vietnamese%20woman%20wearing%20modern%20ao%20dai%20dress%2C%20fashion%20photography%2C%20clean%20white%20background%2C%20professional%20product%20photography&width=100&height=100&seq=cart1&orientation=squarish"
                alt="Product"
                class="w-full h-full object-cover object-top"
              />
            </div>
            <div class="flex-1 p-3">
              <div class="flex justify-between">
                <h3 class="text-sm font-medium">Áo Dài Hiện Đại Phối Hoa</h3>
                <button class="text-gray-400 hover:text-gray-600">
                  <i class="ri-delete-bin-line"></i>
                </button>
              </div>
              <p class="text-xs text-gray-500 mb-2">Màu: Xanh / Size: M</p>
              <div class="flex justify-between items-center">
                <div class="flex items-center">
                  <button
                    class="w-6 h-6 border border-gray-300 rounded flex items-center justify-center"
                  >
                    <i class="ri-subtract-line text-xs"></i>
                  </button>
                  <span class="w-8 text-center text-sm">1</span>
                  <button
                    class="w-6 h-6 border border-gray-300 rounded flex items-center justify-center"
                  >
                    <i class="ri-add-line text-xs"></i>
                  </button>
                </div>
                <span class="font-medium text-primary">1.290.000₫</span>
              </div>
            </div>
          </div>

          <!-- Cart Item 2 -->
          <div class="flex border border-gray-200 rounded-lg overflow-hidden">
            <div class="w-24 h-24">
              <img
                src="https://readdy.ai/api/search-image?query=modern%20smartphone%20on%20clean%20white%20background%2C%20professional%20product%20photography%2C%20high%20resolution&width=100&height=100&seq=cart2&orientation=squarish"
                alt="Product"
                class="w-full h-full object-cover object-top"
              />
            </div>
            <div class="flex-1 p-3">
              <div class="flex justify-between">
                <h3 class="text-sm font-medium">Điện thoại SmartX Pro 2025</h3>
                <button class="text-gray-400 hover:text-gray-600">
                  <i class="ri-delete-bin-line"></i>
                </button>
              </div>
              <p class="text-xs text-gray-500 mb-2">Màu: Đen / Bộ nhớ: 256GB</p>
              <div class="flex justify-between items-center">
                <div class="flex items-center">
                  <button
                    class="w-6 h-6 border border-gray-300 rounded flex items-center justify-center"
                  >
                    <i class="ri-subtract-line text-xs"></i>
                  </button>
                  <span class="w-8 text-center text-sm">1</span>
                  <button
                    class="w-6 h-6 border border-gray-300 rounded flex items-center justify-center"
                  >
                    <i class="ri-add-line text-xs"></i>
                  </button>
                </div>
                <span class="font-medium text-primary">18.990.000₫</span>
              </div>
            </div>
          </div>

          <!-- Cart Item 3 -->
          <div class="flex border border-gray-200 rounded-lg overflow-hidden">
            <div class="w-24 h-24">
              <img
                src="https://readdy.ai/api/search-image?query=luxury%20skincare%20products%20set%20on%20marble%20background%2C%20professional%20beauty%20product%20photography%2C%20clean%20minimal%20aesthetic&width=100&height=100&seq=cart3&orientation=squarish"
                alt="Product"
                class="w-full h-full object-cover object-top"
              />
            </div>
            <div class="flex-1 p-3">
              <div class="flex justify-between">
                <h3 class="text-sm font-medium">
                  Bộ Dưỡng Da Cao Cấp Trà Xanh
                </h3>
                <button class="text-gray-400 hover:text-gray-600">
                  <i class="ri-delete-bin-line"></i>
                </button>
              </div>
              <p class="text-xs text-gray-500 mb-2">Loại: Bộ đầy đủ</p>
              <div class="flex justify-between items-center">
                <div class="flex items-center">
                  <button
                    class="w-6 h-6 border border-gray-300 rounded flex items-center justify-center"
                  >
                    <i class="ri-subtract-line text-xs"></i>
                  </button>
                  <span class="w-8 text-center text-sm">1</span>
                  <button
                    class="w-6 h-6 border border-gray-300 rounded flex items-center justify-center"
                  >
                    <i class="ri-add-line text-xs"></i>
                  </button>
                </div>
                <span class="font-medium text-primary">890.000₫</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Cart Summary -->
        <div class="p-4 border-t border-gray-200 bg-gray-50">
          <div class="space-y-3 mb-4">
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Tạm tính:</span>
              <span>21.170.000₫</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Giảm giá:</span>
              <span>-1.500.000₫</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Phí vận chuyển:</span>
              <span>Miễn phí</span>
            </div>
            <div
              class="flex justify-between font-bold pt-2 border-t border-gray-200"
            >
              <span>Tổng cộng:</span>
              <span class="text-primary">19.670.000₫</span>
            </div>
          </div>

          <div class="space-y-3">
            <button
              class="w-full bg-primary text-white py-3 rounded-button font-medium hover:bg-primary/90 whitespace-nowrap"
            >
              Thanh toán ngay
            </button>
            <button
              class="w-full bg-white border border-gray-300 text-gray-800 py-3 rounded-button font-medium hover:bg-gray-50 whitespace-nowrap"
            >
              Tiếp tục mua sắm
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Scripts -->
    <script id="modalScript">
      document.addEventListener("DOMContentLoaded", function () {
        const quickViewModal = document.getElementById("quickViewModal");
        const modalOverlay = document.getElementById("modalOverlay");
        const closeModal = document.getElementById("closeModal");

        // Function to open modal
        function openQuickViewModal() {
          quickViewModal.classList.remove("hidden");
          document.body.style.overflow = "hidden";
        }

        // Function to close modal
        function closeQuickViewModal() {
          quickViewModal.classList.add("hidden");
          document.body.style.overflow = "";
        }

        // Add click event to all product cards (for demo purposes)
        document.querySelectorAll(".product-card").forEach((card) => {
          card.addEventListener("click", openQuickViewModal);
        });

        // Close modal when clicking close button or overlay
        closeModal.addEventListener("click", closeQuickViewModal);
        modalOverlay.addEventListener("click", closeQuickViewModal);
      });
    </script>

    <script id="cartScript">
      document.addEventListener("DOMContentLoaded", function () {
        const cartSidebar = document.getElementById("cartSidebar");
        const closeCart = document.getElementById("closeCart");

        // Function to open cart
        function openCart() {
          cartSidebar.classList.remove("translate-x-full");
          document.body.style.overflow = "hidden";
        }

        // Function to close cart
        function closeCartSidebar() {
          cartSidebar.classList.add("translate-x-full");
          document.body.style.overflow = "";
        }

        // Add click event to all cart icons
        document.querySelectorAll(".ri-shopping-cart-2-line").forEach((icon) => {
          const parent = icon.closest("div");
          if (parent) {
            parent.addEventListener("click", openCart);
          }
        });

        // Close cart when clicking close button
        closeCart.addEventListener("click", closeCartSidebar);
      });
    </script>

    <script id="quantityScript">
      document.addEventListener("DOMContentLoaded", function () {
        // Quantity input functionality
        const quantityContainers = document.querySelectorAll(".flex items-center");

        quantityContainers.forEach((container) => {
          const minusBtn = container.querySelector("button:first-child");
          const plusBtn = container.querySelector("button:last-child");
          const input = container.querySelector('input[type="number"]');

          if (minusBtn && plusBtn && input) {
            minusBtn.addEventListener("click", () => {
              const currentValue = parseInt(input.value);
              if (currentValue > 1) {
                input.value = currentValue - 1;
              }
            });

            plusBtn.addEventListener("click", () => {
              const currentValue = parseInt(input.value);
              input.value = currentValue + 1;
            });
          }
        });
      });
    </script>
  </body>
</html>
