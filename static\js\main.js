// ShopSocial JavaScript functionality

class ShopSocial {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupModal();
        this.setupSearch();
        this.setupCart();
        this.setupWishlist();
    }

    setupEventListeners() {
        // Mobile menu toggle
        const mobileMenuBtn = document.querySelector('#mobile-menu-btn');
        if (mobileMenuBtn) {
            mobileMenuBtn.addEventListener('click', this.toggleMobileMenu.bind(this));
        }

        // Product quick actions
        document.addEventListener('click', (e) => {
            if (e.target.closest('.quick-action-btn')) {
                this.handleQuickAction(e);
            }
        });

        // Follow/Unfollow buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.follow-btn')) {
                this.handleFollowAction(e);
            }
        });

        // Add to cart buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.add-to-cart-btn')) {
                this.handleAddToCart(e);
            }
        });

        // Buy now buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.buy-now-btn')) {
                this.handleBuyNow(e);
            }
        });

        // Like/Unlike buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.like-btn')) {
                this.handleLikeAction(e);
            }
        });

        // Bookmark buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.bookmark-btn')) {
                this.handleBookmarkAction(e);
            }
        });
    }

    setupModal() {
        const modal = document.getElementById('quickViewModal');
        const closeBtn = document.getElementById('closeModal');
        const overlay = document.getElementById('modalOverlay');

        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.closeModal());
        }

        if (overlay) {
            overlay.addEventListener('click', () => this.closeModal());
        }

        // ESC key to close modal
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && modal && !modal.classList.contains('hidden')) {
                this.closeModal();
            }
        });
    }

    setupSearch() {
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.addEventListener('input', this.debounce(this.handleSearch.bind(this), 300));
        }
    }

    setupCart() {
        this.cart = JSON.parse(localStorage.getItem('shopsocial_cart')) || [];
        this.updateCartCount();
    }

    setupWishlist() {
        this.wishlist = JSON.parse(localStorage.getItem('shopsocial_wishlist')) || [];
    }

    toggleMobileMenu() {
        const menu = document.querySelector('#mobile-menu');
        if (menu) {
            menu.classList.toggle('hidden');
        }
    }

    handleQuickAction(e) {
        e.preventDefault();
        const action = e.target.closest('.quick-action-btn').dataset.action;
        const productId = e.target.closest('.product-card').dataset.productId;

        switch (action) {
            case 'like':
                this.toggleLike(productId);
                break;
            case 'cart':
                this.addToCart(productId);
                break;
            case 'share':
                this.shareProduct(productId);
                break;
            case 'quickview':
                this.openQuickView(productId);
                break;
        }
    }

    handleFollowAction(e) {
        e.preventDefault();
        const btn = e.target.closest('.follow-btn');
        const shopId = btn.dataset.shopId;
        
        if (btn.textContent.trim() === 'Theo dõi') {
            btn.textContent = 'Đang theo dõi';
            btn.classList.remove('text-primary');
            btn.classList.add('bg-gray-100', 'text-gray-800', 'px-3', 'py-1', 'rounded-full');
        } else {
            btn.textContent = 'Theo dõi';
            btn.classList.add('text-primary');
            btn.classList.remove('bg-gray-100', 'text-gray-800', 'px-3', 'py-1', 'rounded-full');
        }
    }

    handleAddToCart(e) {
        e.preventDefault();
        const productCard = e.target.closest('.product-card');
        const productId = productCard?.dataset.productId;
        
        if (productId) {
            this.addToCart(productId);
            this.showNotification('Đã thêm vào giỏ hàng!', 'success');
        }
    }

    handleBuyNow(e) {
        e.preventDefault();
        const productCard = e.target.closest('.product-card');
        const productId = productCard?.dataset.productId;
        
        if (productId) {
            // Redirect to checkout or show buy now modal
            console.log('Buy now:', productId);
            this.showNotification('Chuyển đến trang thanh toán...', 'info');
        }
    }

    handleLikeAction(e) {
        e.preventDefault();
        const btn = e.target.closest('.like-btn');
        const productId = btn.dataset.productId;
        this.toggleLike(productId, btn);
    }

    handleBookmarkAction(e) {
        e.preventDefault();
        const btn = e.target.closest('.bookmark-btn');
        const productId = btn.dataset.productId;
        this.toggleBookmark(productId, btn);
    }

    handleSearch(e) {
        const query = e.target.value.trim();
        if (query.length > 2) {
            // Implement search functionality
            console.log('Searching for:', query);
        }
    }

    addToCart(productId) {
        const existingItem = this.cart.find(item => item.id === productId);
        
        if (existingItem) {
            existingItem.quantity += 1;
        } else {
            this.cart.push({
                id: productId,
                quantity: 1,
                addedAt: new Date().toISOString()
            });
        }
        
        this.saveCart();
        this.updateCartCount();
    }

    toggleLike(productId, btn = null) {
        const isLiked = this.wishlist.includes(productId);
        
        if (isLiked) {
            this.wishlist = this.wishlist.filter(id => id !== productId);
        } else {
            this.wishlist.push(productId);
        }
        
        this.saveWishlist();
        
        if (btn) {
            const icon = btn.querySelector('i');
            if (icon) {
                if (isLiked) {
                    icon.classList.remove('ri-heart-fill', 'text-red-500');
                    icon.classList.add('ri-heart-line');
                } else {
                    icon.classList.remove('ri-heart-line');
                    icon.classList.add('ri-heart-fill', 'text-red-500');
                }
            }
        }
    }

    toggleBookmark(productId, btn) {
        const icon = btn.querySelector('i');
        if (icon) {
            if (icon.classList.contains('ri-bookmark-line')) {
                icon.classList.remove('ri-bookmark-line');
                icon.classList.add('ri-bookmark-fill', 'text-primary');
            } else {
                icon.classList.remove('ri-bookmark-fill', 'text-primary');
                icon.classList.add('ri-bookmark-line');
            }
        }
    }

    shareProduct(productId) {
        if (navigator.share) {
            navigator.share({
                title: 'Sản phẩm từ ShopSocial',
                text: 'Xem sản phẩm này trên ShopSocial',
                url: window.location.href
            });
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(window.location.href);
            this.showNotification('Đã sao chép link!', 'success');
        }
    }

    openQuickView(productId) {
        const modal = document.getElementById('quickViewModal');
        if (modal) {
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }
    }

    closeModal() {
        const modal = document.getElementById('quickViewModal');
        if (modal) {
            modal.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }
    }

    updateCartCount() {
        const cartCount = this.cart.reduce((total, item) => total + item.quantity, 0);
        const cartBadge = document.querySelector('.cart-count');
        if (cartBadge) {
            cartBadge.textContent = cartCount;
        }
    }

    saveCart() {
        localStorage.setItem('shopsocial_cart', JSON.stringify(this.cart));
    }

    saveWishlist() {
        localStorage.setItem('shopsocial_wishlist', JSON.stringify(this.wishlist));
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-lg text-white ${
            type === 'success' ? 'bg-green-500' : 
            type === 'error' ? 'bg-red-500' : 'bg-blue-500'
        }`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ShopSocial();
});
