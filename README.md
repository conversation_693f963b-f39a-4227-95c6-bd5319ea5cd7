# ShopSocial - Shopping & Social Platform

Một nền tảng mua sắm xã hội được xây dựng với FastAPI và Tailwind CSS.

## Tính năng

- 🛍️ Giao diện mua sắm hiện đại
- 📱 Responsive design cho mobile và desktop
- 🔍 Tìm kiếm sản phẩm và cửa hàng
- ❤️ Wishlist và giỏ hàng
- 👥 Theo dõi cửa hàng
- 📊 API RESTful với FastAPI
- 🎨 UI/UX với Tailwind CSS

## Cấu trúc dự án

```
shopsocial/
├── main.py                 # FastAPI application
├── requirements.txt        # Python dependencies
├── .env.example           # Environment variables template
├── README.md              # Documentation
├── static/                # Static files
│   ├── css/
│   │   └── style.css      # Custom CSS styles
│   └── js/
│       ├── main.js        # JavaScript functionality
│       └── tailwind.config.js  # Tailwind configuration
└── templates/             # HTML templates
    └── index.html         # Main page template
```

## Cài đặt

1. **Clone repository:**
```bash
git clone <repository-url>
cd shopsocial
```

2. **Tạo virtual environment:**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# hoặc
venv\Scripts\activate     # Windows
```

3. **Cài đặt dependencies:**
```bash
pip install -r requirements.txt
```

4. **Cấu hình environment:**
```bash
cp .env.example .env
# Chỉnh sửa file .env theo nhu cầu
```

5. **Chạy ứng dụng:**
```bash
python main.py
# hoặc
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

6. **Truy cập ứng dụng:**
Mở trình duyệt và truy cập: http://localhost:8000

## API Endpoints

### Products
- `GET /api/products` - Lấy danh sách sản phẩm
- `GET /api/products/{product_id}` - Lấy thông tin sản phẩm
- `GET /api/search?q={query}` - Tìm kiếm sản phẩm

### Shops
- `GET /api/shops` - Lấy danh sách cửa hàng
- `GET /api/shops/{shop_id}` - Lấy thông tin cửa hàng
- `POST /api/follow/toggle` - Theo dõi/Bỏ theo dõi cửa hàng

### Cart & Wishlist
- `POST /api/cart/add` - Thêm sản phẩm vào giỏ hàng
- `POST /api/wishlist/toggle` - Thêm/Xóa sản phẩm khỏi wishlist

## Công nghệ sử dụng

- **Backend:** FastAPI, Python 3.8+
- **Frontend:** HTML5, Tailwind CSS, Vanilla JavaScript
- **Icons:** Remix Icons
- **Fonts:** Inter, Pacifico (Google Fonts)

## Phát triển

### Thêm tính năng mới

1. **Backend (FastAPI):**
   - Thêm route mới trong `main.py`
   - Tạo model dữ liệu với Pydantic
   - Implement business logic

2. **Frontend:**
   - Thêm HTML trong `templates/`
   - Thêm CSS trong `static/css/style.css`
   - Thêm JavaScript trong `static/js/main.js`

### Styling

- Sử dụng Tailwind CSS classes cho styling cơ bản
- Custom styles trong `static/css/style.css`
- Responsive design với Tailwind breakpoints

### JavaScript

- Vanilla JavaScript trong `static/js/main.js`
- Event-driven architecture
- Local storage cho cart và wishlist

## Deployment

### Development
```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### Production
```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
```

### Docker (Optional)
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

## Contributing

1. Fork repository
2. Tạo feature branch
3. Commit changes
4. Push to branch
5. Create Pull Request

## License

MIT License - xem file LICENSE để biết thêm chi tiết.
