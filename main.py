from fastapi import FastAPI, Request
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from typing import List, Optional
from pydantic import BaseModel
import uvicorn

# Initialize FastAPI app
app = FastAPI(
    title="ShopSocial API",
    description="API for ShopSocial - Shopping & Social Platform",
    version="1.0.0"
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Templates
templates = Jinja2Templates(directory="templates")

# Data models
class Product(BaseModel):
    id: str
    name: str
    price: float
    original_price: Optional[float] = None
    discount_percent: Optional[int] = None
    rating: float
    review_count: int
    description: str
    image_url: str
    shop_name: str
    shop_verified: bool = False
    in_stock: bool = True
    category: str

class Shop(BaseModel):
    id: str
    name: str
    description: str
    avatar_url: str
    verified: bool = False
    follower_count: int = 0

class User(BaseModel):
    id: str
    username: str
    email: str
    avatar_url: str
    follower_count: int = 0
    following_count: int = 0

# Sample data
sample_products = [
    Product(
        id="1",
        name="Áo Dài Hiện Đại Phối Hoa",
        price=1290000,
        original_price=1590000,
        discount_percent=19,
        rating=4.5,
        review_count=126,
        description="Áo dài hiện đại với họa tiết hoa tinh tế, phù hợp cho các dịp đặc biệt và lễ hội.",
        image_url="https://readdy.ai/api/search-image?query=elegant%20vietnamese%20woman%20wearing%20modern%20ao%20dai%20dress%2C%20fashion%20photography%2C%20clean%20white%20background%2C%20professional%20product%20photography&width=600&height=600&seq=post1&orientation=squarish",
        shop_name="Boutique Hà Nội",
        shop_verified=True,
        category="fashion"
    ),
    Product(
        id="2",
        name="Điện thoại SmartX Pro 2025",
        price=18990000,
        original_price=21990000,
        discount_percent=14,
        rating=5.0,
        review_count=352,
        description="Điện thoại thông minh mới nhất với camera 108MP, màn hình AMOLED 6.7\" và pin 5000mAh.",
        image_url="https://readdy.ai/api/search-image?query=modern%20smartphone%20on%20clean%20white%20background%2C%20professional%20product%20photography%2C%20high%20resolution&width=600&height=600&seq=post2&orientation=squarish",
        shop_name="TechZone Việt",
        shop_verified=True,
        category="electronics"
    ),
    Product(
        id="3",
        name="Bộ Dưỡng Da Cao Cấp Trà Xanh",
        price=890000,
        original_price=1290000,
        discount_percent=31,
        rating=5.0,
        review_count=218,
        description="Bộ sản phẩm dưỡng da chiết xuất từ trà xanh Đà Lạt, giúp làm sạch, dưỡng ẩm và chống lão hóa.",
        image_url="https://readdy.ai/api/search-image?query=luxury%20skincare%20products%20set%20on%20marble%20background%2C%20professional%20beauty%20product%20photography%2C%20clean%20minimal%20aesthetic&width=600&height=600&seq=post3&orientation=squarish",
        shop_name="BeautyViet Official",
        shop_verified=True,
        category="beauty"
    )
]

sample_shops = [
    Shop(
        id="1",
        name="Boutique Hà Nội",
        description="Cửa hàng chính thức",
        avatar_url="https://readdy.ai/api/search-image?query=vietnamese%20fashion%20boutique%20logo%2C%20minimalist%20design&width=50&height=50&seq=shop1&orientation=squarish",
        verified=True,
        follower_count=1250
    ),
    Shop(
        id="2",
        name="TechZone Việt",
        description="Đại lý ủy quyền",
        avatar_url="https://readdy.ai/api/search-image?query=vietnamese%20tech%20store%20logo%2C%20modern%20design&width=50&height=50&seq=shop2&orientation=squarish",
        verified=True,
        follower_count=3420
    )
]

# Routes
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """Render the main page"""
    return templates.TemplateResponse("index.html", {
        "request": request,
        "products": sample_products,
        "shops": sample_shops
    })

@app.get("/api/products", response_model=List[Product])
async def get_products(
    category: Optional[str] = None,
    search: Optional[str] = None,
    limit: int = 10,
    offset: int = 0
):
    """Get products with optional filtering"""
    products = sample_products
    
    if category:
        products = [p for p in products if p.category == category]
    
    if search:
        products = [p for p in products if search.lower() in p.name.lower() or search.lower() in p.description.lower()]
    
    return products[offset:offset + limit]

@app.get("/api/products/{product_id}", response_model=Product)
async def get_product(product_id: str):
    """Get a specific product by ID"""
    for product in sample_products:
        if product.id == product_id:
            return product
    return {"error": "Product not found"}

@app.get("/api/shops", response_model=List[Shop])
async def get_shops():
    """Get all shops"""
    return sample_shops

@app.get("/api/shops/{shop_id}", response_model=Shop)
async def get_shop(shop_id: str):
    """Get a specific shop by ID"""
    for shop in sample_shops:
        if shop.id == shop_id:
            return shop
    return {"error": "Shop not found"}

@app.post("/api/cart/add")
async def add_to_cart(product_id: str, quantity: int = 1):
    """Add product to cart"""
    return {"message": f"Added {quantity} of product {product_id} to cart"}

@app.post("/api/wishlist/toggle")
async def toggle_wishlist(product_id: str):
    """Toggle product in wishlist"""
    return {"message": f"Toggled product {product_id} in wishlist"}

@app.post("/api/follow/toggle")
async def toggle_follow(shop_id: str):
    """Toggle follow status for a shop"""
    return {"message": f"Toggled follow status for shop {shop_id}"}

@app.get("/api/search")
async def search(q: str, category: Optional[str] = None):
    """Search products and shops"""
    products = [p for p in sample_products if q.lower() in p.name.lower() or q.lower() in p.description.lower()]
    shops = [s for s in sample_shops if q.lower() in s.name.lower() or q.lower() in s.description.lower()]
    
    if category:
        products = [p for p in products if p.category == category]
    
    return {
        "products": products,
        "shops": shops,
        "query": q
    }

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
